/**
 * 题型数据处理工具
 * 用于处理流式数据并转换为组件需要的格式
 */
import { nanoid } from '@sa/utils'

/**
 * 处理单选题数据
 * @param data 原始流式数据，包含题目信息
 * @returns 处理后的单选题数据，符合ProcessedQuestionData接口格式
 */
function processSingleChoiceData(data: Question.QuestionData): Question.ProcessedQuestionData {
  // 解构原始数据并设置默认值
  const {
    QuestionType: typeText, // 题型文本描述
    QuestionTypeId: typeId, // 题型ID
    Title: title, // 题目内容
    Options = [], // 选项数组，默认为空数组
    Answer: correctAnswer, // 正确答案
    Analysis, // 答案解析
    KnowledgePoints, // 知识点数组，默认为空数组
  } = data.Question

  // 返回处理后的数据结构
  return {
    id: nanoid(), // 使用nanoid生成唯一ID
    componentsName: getQuestionComponentName(typeId), // 获取对应组件名称
    typeText, // 题型文本
    typeId, // 题型ID
    title, // 题目内容
    options: Options?.map(option => ({ // 转换选项格式
      label: option.Content, // 选项显示文本
      value: option.Option, // 选项值
    })),
    correctAnswer, // 正确答案
    analysis: Analysis, // 答案解析
    knowledgePoints: KnowledgePoints || [], // 将知识点对象数组转换为字符串数组
  }
}

/**
 * 处理多选题数据
 */
function processMultipleChoiceData(data: Question.QuestionData): Question.ProcessedQuestionData {
  const {
    QuestionType: typeText, // 题型文本描述
    QuestionTypeId: typeId, // 题型ID
    Title: title, // 题目内容
    Options = [], // 选项数组，默认为空数组
    Answer: correctAnswer, // 正确答案
    Analysis, // 答案解析
    KnowledgePoints, // 知识点数组，默认为空数组
  } = data.Question

  return {
    id: nanoid(), // 使用nanoid生成唯一ID
    componentsName: getQuestionComponentName(typeId), // 获取对应组件名称
    typeText, // 题型文本
    typeId, // 题型ID
    title, // 题目内容
    options: Options?.map(option => ({ // 转换选项格式
      label: option.Content, // 选项显示文本
      value: option.Option, // 选项值
    })),
    correctAnswer,
    analysis: Analysis,
    knowledgePoints: KnowledgePoints || [],
  }
}

/**
 * 主要的数据处理函数
 * 根据题型ID处理不同类型的题目数据
 */
export function processQuestionData(data: Question.QuestionData): Question.ProcessedQuestionData {
  const questionTypeId = data.Question.QuestionTypeId
  switch (questionTypeId) {
    case '2': // 单选题
      return processSingleChoiceData(data)
    case '11': // 判断题
      return processSingleChoiceData(data)
    case '10': // 多选题
      return processMultipleChoiceData(data)
    default:
      return processSingleChoiceData(data)
  }
}

/**
 * 获取题型对应的组件名称
 */
function getQuestionComponentName(typeId: string): string {
  const componentMap: Record<string, string> = {
    2: 'SingleChoice', // 单项选择题
    11: 'TrueFalse', // 判断题
    10: 'MultipleChoice', // 多项选择题
    4: 'FillBlank',
  }

  return componentMap[typeId] || 'SingleChoice'
}

/**
 * 将前端题目数据转换为API所需的GeneratedQuestion格式
 * @param question 前端处理后的题目数据
 * @returns 转换后的GeneratedQuestion格式数据
 */
export function convertToGeneratedQuestion(question: Question.ProcessedQuestionData): QuestionsApi.GeneratedQuestion {
  if (!question) {
    throw new Error('题目数据不能为空')
  }

  // 转换选项格式
  const options: GeneratedQuestionOption[] | undefined = question.options?.map(option => ({
    Content: option.label || '',
    Option: option.value || '',
  }))

  // 转换知识点格式（如果有的话）
  const knowledgePoints: GeneratedQuestionKnowledgePoint[] | null = question.knowledgePoints?.length > 0
    ? question.knowledgePoints.map((point, index) => ({
        Content: point || '',
        Id: `kp_${question.id}_${index}`, // 使用题目ID生成更唯一的知识点ID
        Level: 1, // 默认层级，实际使用时可能需要真实的层级信息
      }))
    : undefined

  return {
    Analysis: question.analysis || '',
    Answer: question.correctAnswer || '',
    Chapters: undefined, // 前端数据中没有章节信息，设为undefined
    KnowledgePoints: knowledgePoints || null,
    Options: options,
    QuestionType: question.typeText || '',
    QuestionTypeId: question.typeId || '',
    Title: question.title || '',
  }
}

/**
 * 批量转换题目数据
 * @param questions 前端题目数据数组
 * @returns 转换后的GeneratedQuestion格式数据数组
 */
export function convertQuestionsToGeneratedQuestions(questions: Question.ProcessedQuestionData[]): GeneratedQuestion[] {
  if (!Array.isArray(questions)) {
    throw new TypeError('题目数据必须是数组格式')
  }

  return questions.map(question => convertToGeneratedQuestion(question))
}
